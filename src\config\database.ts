import { Pool, PoolConfig } from 'pg';
import { config } from './environment';

/**
 * PostgreSQL database configuration
 */
const dbConfig: PoolConfig = config.database.url ? {
  connectionString: config.database.url,
  ssl: {
    rejectUnauthorized: false // Required for some cloud PostgreSQL providers like Neon
  },
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 10000, // How long to wait for a connection to become available
} : {
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.username,
  password: config.database.password,
  ssl: {
    rejectUnauthorized: false // Required for some cloud PostgreSQL providers like Neon
  },
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 10000, // How long to wait for a connection to become available
};

/**
 * Create a new PostgreSQL connection pool
 */
const pool = new Pool(dbConfig);

// Log connection events
pool.on('connect', () => {
  console.log('Connected to PostgreSQL database');
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle PostgreSQL client', err);
  process.exit(-1);
});

/**
 * Execute a query on the PostgreSQL database
 * @param text - SQL query text
 * @param params - Query parameters
 * @returns Query result
 */
export const query = async (text: string, params?: any[]) => {
  const start = Date.now();
  try {
    const res = await pool.query(text, params);
    const duration = Date.now() - start;
    console.log('Executed query', { text, duration, rows: res.rowCount });
    return res;
  } catch (error) {
    console.error('Error executing query', { text, error });
    throw error;
  }
};

/**
 * Get a client from the connection pool
 * @returns PostgreSQL client
 */
export const getClient = async () => {
  return await pool.connect();
};

/**
 * Initialize database connection
 */
export const initDatabase = async (): Promise<void> => {
  try {
    // Test database connection with timeout
    const client = await Promise.race([
      pool.connect(),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database connection timeout')), 10000)
      )
    ]) as any;

    console.log('✅ Successfully connected to PostgreSQL database');
    client.release();

    // You can add database initialization logic here
    // For example, creating tables if they don't exist

  } catch (error) {
    console.warn('⚠️  Database connection failed, but server will continue without database:', error);
    // Don't throw error - allow server to start without database
  }
};

export default {
  query,
  getClient,
  initDatabase,
  pool,
};
