import { Request, Response } from 'express';
import { DatabaseService } from '../services/database.service';

/**
 * Database controller for testing database operations
 */
export class DatabaseController {
  /**
   * Test database connection
   */
  static async testConnection(_req: Request, res: Response): Promise<void> {
    try {
      const isConnected = await DatabaseService.testConnection();
      
      if (isConnected) {
        res.status(200).json({
          success: true,
          message: 'Database connection successful',
          timestamp: new Date().toISOString()
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Database connection failed',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Database connection test error:', error);
      res.status(500).json({
        success: false,
        message: 'Database connection test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get database information
   */
  static async getDatabaseInfo(_req: Request, res: Response): Promise<void> {
    try {
      const version = await DatabaseService.getDatabaseVersion();
      
      res.status(200).json({
        success: true,
        data: {
          version,
          connected: true
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get database info error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get database information',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Execute a custom query (for testing purposes)
   */
  static async executeQuery(req: Request, res: Response): Promise<void> {
    try {
      const { query: sqlQuery, params } = req.body;
      
      if (!sqlQuery) {
        res.status(400).json({
          success: false,
          message: 'SQL query is required',
          timestamp: new Date().toISOString()
        });
        return;
      }

      // Only allow SELECT queries for safety
      if (!sqlQuery.trim().toLowerCase().startsWith('select')) {
        res.status(400).json({
          success: false,
          message: 'Only SELECT queries are allowed',
          timestamp: new Date().toISOString()
        });
        return;
      }

      const result = await DatabaseService.executeQuery(sqlQuery, params);
      
      res.status(200).json({
        success: true,
        data: {
          rows: result.rows,
          rowCount: result.rowCount,
          fields: result.fields?.map((field: any) => ({
            name: field.name,
            dataTypeID: field.dataTypeID
          }))
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Execute query error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to execute query',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    }
  }
}
